import{a}from"./chunk-7KZRW4CE.js";var c=0,y=Array.isArray;function m(o,n,i,p,s,u){n||(n={});var t,e,r=n;if("ref"in r)for(e in r={},n)e=="ref"?t=n[e]:r[e]=n[e];var f={type:o,props:r,key:i,ref:t,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--c,__i:-1,__u:0,__source:s,__self:u};if(typeof o=="function"&&(t=o.defaultProps))for(e in t)r[e]===void 0&&(r[e]=t[e]);return a.vnode&&a.vnode(f),f}export{m as a};
