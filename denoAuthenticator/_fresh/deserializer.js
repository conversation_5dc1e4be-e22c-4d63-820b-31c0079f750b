var i="_f";function y(t){let s=atob(t),r=s.length,c=new Uint8Array(r);for(let a=0;a<r;a++)c[a]=s.charCodeAt(a);return c}var u="Invalid reference";function h(t,s){for(let r of s)if(r!==null){if(r!=="value"&&!Object.hasOwn(t,r))throw new Error(u);t=t[r]}return t}function k(t,s){function r(g,e){if(typeof e=="object"&&e&&i in e){let n=e;if(n[i]==="s")return s(n.v);if(n[i]==="b")return BigInt(n.d);if(n[i]==="u8a")return y(n.d);if(n[i]==="l"){let o=n.v;return o[i]=n.k,o}throw new Error(`Unknown key: ${n[i]}`)}return e}let{v:c,r:a}=JSON.parse(t,r),l=a??[];for(let[g,...e]of l){let n=h(c,g);for(let o of e){if(o.length===0)throw new Error(u);let w=h(c,o.slice(0,-1)),f=o[o.length-1];if(f!=="value"&&!Object.hasOwn(w,f))throw new Error(u);w[f]=n}}return c}export{k as deserialize};
