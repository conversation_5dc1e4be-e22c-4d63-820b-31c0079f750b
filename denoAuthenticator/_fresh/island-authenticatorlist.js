import{b as s,c as d}from"./chunk-FJSXHW3L.js";import{a as t}from"./chunk-R54PQP4Z.js";import{a}from"./chunk-5FHJHB3U.js";import"./chunk-7KZRW4CE.js";function v({authenticators:o}){let l=s(Date.now()),i=s(null);a(()=>{let e=setInterval(()=>{l.value=Date.now()},1e3);return()=>clearInterval(e)},[]);let m=d(()=>o.map(e=>{let c=30-Math.floor(l.value/1e3)%30;return{...e,remainingTime:c,progressPercentage:c/30*100}})),u=async(e,n)=>{try{await navigator.clipboard.writeText(e),i.value=n,setTimeout(()=>{i.value=null},2e3)}catch(r){console.error("\u590D\u5236\u5931\u8D25:",r)}},g=async(e,n)=>{if(confirm(`\u786E\u5B9A\u8981\u5220\u9664\u8BA4\u8BC1\u5668 "${n}" \u5417\uFF1F`))try{(await fetch(`/api/authenticators/${e}`,{method:"DELETE"})).ok?window.location.reload():alert("\u5220\u9664\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}catch(r){console.error("\u5220\u9664\u5931\u8D25:",r),alert("\u5220\u9664\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}};return o.length===0?null:t("div",{class:"space-y-4",children:m.value.map(e=>t("div",{class:"bg-white overflow-hidden shadow rounded-lg border border-gray-200",children:t("div",{class:"px-4 py-5 sm:p-6",children:t("div",{class:"flex items-center justify-between",children:[t("div",{class:"flex-1",children:t("div",{class:"flex items-center",children:[t("div",{class:"flex-shrink-0",children:t("div",{class:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:t("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})})})}),t("div",{class:"ml-4",children:[t("h3",{class:"text-lg font-medium text-gray-900",children:e.name}),e.issuer&&t("p",{class:"text-sm text-gray-500",children:e.issuer}),e.account_name&&t("p",{class:"text-xs text-gray-400",children:e.account_name})]})]})}),t("div",{class:"flex items-center space-x-4",children:[t("div",{class:"text-center",children:[t("div",{class:"text-2xl font-mono font-bold text-gray-900 tracking-wider",children:e.code}),t("div",{class:"text-xs text-gray-500 mt-1",children:[e.remainingTime,"\u79D2\u540E\u5237\u65B0"]}),t("div",{class:"w-16 bg-gray-200 rounded-full h-1 mt-2",children:t("div",{class:`h-1 rounded-full transition-all duration-1000 ${e.remainingTime<=5?"bg-red-500":e.remainingTime<=10?"bg-yellow-500":"bg-green-500"}`,style:{width:`${e.progressPercentage}%`}})})]}),t("div",{class:"flex flex-col space-y-2",children:[t("button",{onClick:()=>u(e.code,e.id),class:`px-3 py-1 rounded text-xs font-medium transition-colors ${i.value===e.id?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800 hover:bg-blue-200"}`,children:i.value===e.id?"\u5DF2\u590D\u5236!":"\u590D\u5236"}),t("button",{onClick:()=>g(e.id,e.name),class:"px-3 py-1 rounded text-xs font-medium bg-red-100 text-red-800 hover:bg-red-200 transition-colors",children:"\u5220\u9664"})]})]})]})})},e.id))})}export{v as default};
