import{b as o}from"./chunk-BXFWGBH3.js";import{a as r}from"./chunk-R54PQP4Z.js";import"./chunk-5FHJHB3U.js";import{g as e}from"./chunk-7KZRW4CE.js";var u=e([]);var S=e(void 0);function c(t){return t.children}c.displayName="Partial";function n(t){return r("button",{...t,disabled:!o||t.disabled,class:"px-2 py-1 border-gray-500 border-2 rounded bg-white hover:bg-gray-200 transition-colors"})}function s(t){return r("div",{class:"flex gap-8 py-6",children:[r(n,{onClick:()=>t.count.value-=1,children:"-1"}),r("p",{class:"text-3xl tabular-nums",children:t.count}),r(n,{onClick:()=>t.count.value+=1,children:"+1"})]})}export{s as default};
