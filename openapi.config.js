/* eslint-disable */
const generator = require("./generator");

const schemaConfig = {
  manager: {
    schemaPath: "http://121.4.24.16:9201/v3/api-docs",
    projectName: "manager",
    namespace: "MANAGER",
  },
  auth: {
    // schemaPath: "http://121.4.24.16:9200/v3/api-docs",
    schemaPath: "http://150.158.141.82:9201/v3/api-docs",
    projectName: "auth",
    namespace: "AUTH",
  },
  mem: {
    // schemaPath: "http://150.158.141.82:9400/v3/api-docs",
    schemaPath: "http://150.158.141.82:9400/v3/api-docs",
    projectName: "mem",
    namespace: "MEN",
  },
  business: {
    // schemaPath: "http://121.4.24.16:9400/v3/api-docs",
    schemaPath: "http://150.158.141.82:9400/v3/api-docs",
    projectName: "business",
    namespace: "BUSINESS",
  },
  memType: {
    schemaPath: "http://121.4.24.16:9304/v3/api-docs",
    projectName: "memType",
    namespace: "MEMTYPE",
  },
  memRecord: {
    schemaPath: "http://150.158.141.82:9400/v3/api-docs",
    projectName: "memRecord",
    namespace: "MEMRECORD",
  },

  businessRelation: {
    schemaPath: "http://150.158.141.82:9400/v3/api-docs",
    projectName: "businessRelation",
    namespace: "BUSINESSRELATION",
  },
  member: {
    schemaPath: "http://150.158.141.82:9400/v3/api-docs",
    projectName: "member",
    namespace: "MEMBER",
  },
};

generator(
  schemaConfig.auth,
  [
    // "/auth/login", // 登录
    "/system/auth/refresh",
    "/system/auth/logout",
  ],
  true
);

generator(
  schemaConfig.business,
  [
    // "/business/reservation-record",
    // "/business/reservation-record/list",
    // "/business/reservation-record/{reservationRecordId}",
    // "/business/reservation-template",
    // "/business/reservation-template/edit",
    // "/business/reservation-template",
    // "/business/reservation-record/get-member-type",
    // "/business/employee/list",
    // "/business/employee/{employeeId}",
    // "/business/employee",
    // "/business/reservation-template/project-list",
    // "/business/shop",
    // '/business/reservation-record/get-member-type',
    // "/business/shop/license",
    // "/business/shop/file/upload",
    // '/business/shop-project/all-category',
    '/business/shop/get-user-bind-business'
  ],
  true
);

// generator(
//   schemaConfig.system,
//   [
//     "/system/region/city/{adcode}",
//     "/system/region/district/{adcode}",
//     "/system/region/province"
//   ],
//   true
// );

// generator(
// 	schemaConfig.business,
// 	[
// 		// "/business/dashboard/revenue",
// 		// "/business/dashboard/traffic",
// 		// "/business/reservation-record/list",
// 		// "/business/shop",
// 		// "/business/mem/cardtemplate",
// 		// "/business/shop-project/all",
// 		// "/business/reservation-record/accept",
// 		// "/business/reservation-record/edit-reservation-date",
// 		// "/business/mem/cardtemplate/{templateIds}",
// 		// "/business/mem/cardtemplate",
// 		// "/business/mem/cardtemplate/{templateId}",
// 		// "/business/mem/cardtemplate"
// 		// "/business/relation/get-member",
// 		// "/business/member/record/addNew",
// 		// "/business/member",
// 		// "/business/member/record/detailListNew"
// 		// 
// 		// "/business/member/record/walkAddNew",
// 		// "/business/member/record/detailListNew"
// 		// "/business/customer-resource/{customerResourceId}",
// 		// "/business/customer-resource/list",
// 		// "/business/customer-resource/ban",
// 		// "/business/customer-resource/follow-up-record/{customerResourceId}",
// 		// "/business/mem/cardtemplate/{templateId}",
// 		// "/business/member/record/detailListNew",
// 		// "/business/customer-resource/add-follow-up-record"
// 		// "/business/customer-resource/basic/{customerResourceId}"
// 		// "/business/customer-resource"
// 		// '/business/mem/cardtemplate/{templateIds}'
// 	],
// 	true
// );

// generator(
//   schemaConfig.member,
//   ['/business/member/detaillist', '/business/member/dataDetail'],
//   true
// );

// generator(
//   schemaConfig.mem,
//   [
//     '/business/mem/type/list', // 查询会员类型列表
//     '/business/mem/type/detaillist', // 查询会员类型详情列表
//     '/business/mem/type', // 新增会员类型 ｜ 更新会员类型
//     '/business/mem/type/{cardTypeId}', // 查询会员类型详情 ｜ 删除会员类型
//     '/business/mem/member/{memberId}', // 查询会员详情 ｜ 删除会员
//     '/business/mem/member', // 新增会员 ｜ 更新会员
//     '/business/mem/cardtemplate/list', // 会员卡列表
//   ],
//   true
// );

// generator(
//   schemaConfig.member,
//   ['/business/member/detaillist', '/business/member/dataDetail'],
//   true
// );

// generator(
//   schemaConfig.businessRelation,
//   [
//     '/business/relation/cardRTSummarylist', // 查询卡券关系汇总列表
//     '/business/relation/cardRTMemberList', // 查询卡券关系会员列表
//     '/business/relation/rSetmealList', // 查询卡券关系会员详情列表
//   ],
//   true
// );

// generator(
//     schemaConfig.memRecord, [
//         '/business/record/detaillist', // 查询卡券关系汇总列表
//         '/business/record/memberlistSummary' // 查询卡券关系会员列表
//     ],
//     true
// );

// generator(
//   schemaConfig.businessRelation,
//   [
//     '/business/relation/cardRTSummarylist', // 查询卡券关系汇总列表
//     '/business/relation/cardRTMemberList', // 查询卡券关系会员列表
//     '/business/relation/rSetmealList', // 查询卡券关系会员详情列表
//   ],
//   true
// );

// generator(
//     schemaConfig.memRecord, [
//         '/business/record/detaillist', // 查询卡券关系汇总列表
//         '/business/record/memberlistSummary' // 查询卡券关系会员列表
//     ],
//     true
// );

// generator(
//   schemaConfig.mem,
//   [
//     '/business/mem/type/list', // 查询会员类型列表
//     '/business/mem/type/detaillist', // 查询会员类型详情列表
//     '/business/mem/type', // 新增会员类型 ｜ 更新会员类型
//     '/business/mem/type/{cardTypeId}', // 查询会员类型详情 ｜ 删除会员类型
//     '/business/mem/member/{memberId}', // 查询会员详情 ｜ 删除会员
//     '/business/mem/member', // 新增会员 ｜ 更新会员
//     '/business/mem/cardtemplate/list', // 会员卡列表
//   ],
//   true
// );
