import { View, Input, Button, Label, Image, RichText, ITouchEvent } from "@tarojs/components";
import Taro from "@tarojs/taro";
import useWindowArea from "@/hook/windowArea";
import React, { useState, useRef } from "react";
import { isDev } from "@/constant/env";
import { validatePhone } from "@/util/validate-util";

interface IUserAccount {
  username: string,
  password: string,
  agreeProtocol: boolean
}

interface ISmsForm {
  phone: string,
  verifyCode: string
}

type LoginTab = 'password' | 'sms';

const COUNTDOWN = 60;

const LoginPage: React.FC = () => {
  const { bottomArea } = useWindowArea()

  // 标签页状态
  const [activeTab, setActiveTab] = useState<LoginTab>('password');

  // 账号密码登录状态
  const [userAccount, setUserAccount] = useState<IUserAccount>({
    username: isDev ? "admin" : "",
    password: isDev ? "admin123" : "",
    agreeProtocol: isDev ? true : false
  });

  // 短信验证码登录状态
  const [smsForm, setSmsForm] = useState<ISmsForm>({
    phone: "",
    verifyCode: ""
  });

  // 验证码发送状态
  const [showSend, setShowSend] = useState(true);
  const [countdown, setCountdown] = useState(COUNTDOWN);
  const countdownRef = useRef(COUNTDOWN);
  const timer = useRef<NodeJS.Timeout>();

  const [agreeProtocol, setAgreeProtocol] = useState(isDev ? true : false);

  useMount(() => {
    const existInfo = Taro.getStorageSync("loginInfo");
    if (existInfo) {
      // 若之前有登录过，则默认勾选同意用户协议
      setAgreeProtocol(true);
      Taro.removeStorageSync("loginInfo");
    }
  });

  // 更新账号state信息
  const updateAccount = (params: Record<string, string>) => {
    setUserAccount(state => ({
      ...state,
      ...params
    }))
  }

  // 更新短信表单信息
  const updateSmsForm = (params: Record<string, string>) => {
    setSmsForm(state => ({
      ...state,
      ...params
    }))
  }

  // 发送验证码
  const sendVerifyCode = async () => {
    if (!smsForm.phone.trim()) {
      Taro.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }

    if (!validatePhone(smsForm.phone)) {
      Taro.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      });
      return;
    }

    try {
      service.auth.tokenController.postAuthSendSmsCode({
        phonenumber: smsForm.phone
      }).then(_res => {
        setShowSend(false);
        Taro.showToast({
          title: '获取成功，请查收短信验证码',
          icon: 'none'
        })
        timer.current = setInterval(() => {
          setCountdown(--countdownRef.current);
          if (countdownRef.current === 0) {
            setShowSend(true)
            clearInterval(timer.current);
            countdownRef.current = COUNTDOWN;
          }
        }, 1000)
      })
    } catch (error) {
      Taro.showToast({
        title: '获取验证码失败',
        icon: 'none'
      })
    }
  }

  // 短信验证码登录
  const loginWithSms = async () => {
    if (!smsForm.phone.trim()) {
      Taro.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }

    if (!validatePhone(smsForm.phone)) {
      Taro.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      });
      return;
    }

    if (!smsForm.verifyCode.trim()) {
      Taro.showToast({
        title: '请输入验证码',
        icon: 'none'
      })
      return
    }

    if (!agreeProtocol) {
      Taro.showToast({
        title: '请认真阅读并勾选同意《用户协议》和《隐私政策》',
        icon: 'none'
      })
      return
    }

    Taro.showLoading({
      title: "登录中"
    });

    try {
      const res = await service.auth.tokenController.postLoginSmsBusiness({
        phonenumber: smsForm.phone,
        smscode: smsForm.verifyCode
      })
      if (res) {
        setLoginInfo(res?.access_token);
      }
    } catch (error) {
      Taro.showToast({
        title: '登录失败',
        icon: 'error'
      })
    } finally {
      Taro.hideLoading();
    }
  }

  // 验证码登录（切换到短信标签页）
  const toVerifyCodeLogin = () => {
    setActiveTab('sms');
  }

  // 查看用户协议
  const toViewProtocol = (e: ITouchEvent) => {
    e.stopPropagation();
    //todo 打开查看用户协议
  }

  // 查看隐私政策
  const toViewPolicy = (e: ITouchEvent) => {
    e.stopPropagation();
    //todo 打开查看用户协议
  }

  /**
   * 账号密码登录
   * @returns 
   */
  const login = async () => {
    if (!userAccount.username.trim()) {
      Taro.showToast({
        title: '请输入用户名/手机号',
        icon: 'none'
      })
      return
    }

    if (!userAccount.password.trim()) {
      Taro.showToast({
        title: '请输入密码',
        icon: 'none'
      })
      return
    }

    if (!agreeProtocol) {
      Taro.showToast({
        title: '请认真阅读并勾选同意《用户协议》和《隐私政策》',
        icon: 'none'
      })
      return
    }

    Taro.showLoading({
      title: "登录中"
    });
    try {
      const res = await service.auth.tokenController
        .login(userAccount).catch((err) => {
          Taro.showToast({
            title: err.data?.msg ?? "登录失败",
            icon: "error",
            duration: 2000
          })
        });
      if (res) {
        setLoginInfo(res?.access_token);
      }
    } catch (error) {
      Taro.showToast({
        title: "登录失败",
        icon: "error",
        duration: 2000
      })
    } finally {
      Taro.hideLoading();
    }
  }

  /**
   * 通过手机号登录
   * @param e 微信手机号登录回调事件
   */
  const phoneLogin = (e: any) => {
    if (e && e.detail && e.detail.errMsg == "getPhoneNumber:ok") {
      if (!agreeProtocol) {
        Taro.showToast({
          title: '请认真阅读并勾选同意《用户协议》和《隐私政策》',
          icon: 'none'
        })
        return
      }
      businessWxLogin(e.detail);
    } else {
      Taro.showToast({
        title: '用户拒绝授权',
        icon: 'none',
      });
    }
  }

  /**
   * 用户授权后走微信登录流程
   * @param weiXinLoginParam 微信账号参数
   */
  const businessWxLogin = (weiXinLoginParam: any) => {
    Taro.login({
      success: async (res) => {
        Taro.showLoading({
          title: "登录中"
        });
        try {
          weiXinLoginParam.code = res.code;
          const loginRes = await service.auth.tokenController.businessLoginWx(weiXinLoginParam).catch((err) => {
            Taro.showToast({
              title: err.data?.msg ?? "登录失败",
              icon: "error",
              duration: 2000
            })
          });
          if (loginRes) {
            setLoginInfo(loginRes?.access_token);
          }
        } catch (error) {
          Taro.showToast({
            title: "登录失败",
            icon: "error",
            duration: 2000
          })
        }
        Taro.hideLoading()
      },
      fail: (err) => {
        console.log(err);
      },
    })
  }

  // 设置用户登录态缓存
  const setLoginInfo = async (token: string) => {
    if (!token) {
      return
    }
    let loginInfo: any = {};
    loginInfo.token = token;
    Taro.setStorageSync("loginInfo", loginInfo);
    try {
      const res = await service.business.shangjiaruzhudianpuguanli.get();
      if (res && res.length) {
        // 默认使用第一个店铺
        const business = res[0];
        loginInfo.businessId = business.businessId;
        //todo 保存用户的店铺信息到缓存
        Taro.setStorageSync("loginInfo", loginInfo);
        // 微信7.0.5版本后，页面配置中的 disableSwipeBack 属性将不再生效，因此在登录页无法阻止用户通过手机返回上一个页面
        // 登录后固定跳转到首页，无法返回因令牌时效需要重新登录之前的页面，因大部分页面的返回功能都使用navigateBack API, 
        // 重定向到原来的页面会导致应用无法返回其他页面
        // 
        // redirect 防止用户通过手势返回登录页
        Taro.redirectTo({ url: "/view/page/tab/home/<USER>" })
      } else {
        // 没有店铺信息，转向到店铺注册页面
        Taro.redirectTo({
          url: "/shop/register-business/register-business-page"
        })
      }
    } catch (error) {

    }

  }

  return (
    <View className="w-full h-full flex flex-col bg-cover bg-center overflow-hidden box-border"
      style={{
        backgroundImage: `url(${require("@/assets/image/login/bg.png")})`,
        paddingBottom: bottomArea
      }}>
      {/* 顶部欢迎区域 */}
      <View className="h-[265px] relative">
        <View className="absolute bottom-[45px] left-6 text-[28px] font-bold">
          <RichText nodes="欢迎登录"></RichText>
        </View>
        <Image
          className="absolute bottom-[11px] right-[18px] w-[167px]"
          src={require("@/assets/image/login/main.png")}
          mode="widthFix"
        />
      </View>

      {/* 标签页导航 */}
      <View className="px-5 mb-4">
        <View className="flex bg-white/90 rounded-2xl p-1 shadow-lg backdrop-blur-sm">
          <View
            className={`flex-1 text-center py-3 rounded-xl transition-all duration-300 ${activeTab === 'password'
              ? 'bg-white text-[#FDD244] font-bold shadow-md'
              : 'text-[#999] font-medium'
              }`}
            onClick={() => setActiveTab('password')}
          >
            账号密码登录
          </View>
          <View
            className={`flex-1 text-center py-3 rounded-xl transition-all duration-300 ${activeTab === 'sms'
              ? 'bg-white text-[#FDD244] font-bold shadow-md'
              : 'text-[#999] font-medium'
              }`}
            onClick={() => setActiveTab('sms')}
          >
            短信注册登录
          </View>
        </View>
      </View>

      {/* 表单区域 */}
      <View className="px-5 mt-2">
        <View className="bg-white/90 rounded-2xl p-5 shadow-lg backdrop-blur-sm">
          {activeTab === 'password' ? (
            // 账号密码登录表单
            <>
              <View className="mb-4">
                <View className="text-[#1f1f1f] font-bold mb-3 text-base flex items-center">
                  <View className="w-1 h-4 bg-[#FDD244] rounded-full mr-2"></View>
                  用户名/手机号
                </View>
                <View className="bg-[#f8f9fa] rounded-xl p-3 border border-[#e9ecef] flex items-center">
                  <Input
                    className="bg-transparent text-base text-[#333] flex-1"
                    value={userAccount.username}
                    placeholder='请输入用户名/手机号'
                    placeholderClass="text-[#999]"
                    cursorSpacing={20}
                    onInput={e => updateAccount({ username: e.detail.value })}
                  />
                  {userAccount.username && (
                    <View
                      className="w-6 h-6 bg-[#d1d5db] rounded-full flex items-center justify-center ml-2 active:bg-[#9ca3af]"
                      onClick={() => updateAccount({ username: '' })}
                    >
                      <View className="text-white text-sm font-bold leading-none">×</View>
                    </View>
                  )}
                </View>
              </View>
              <View className="mb-2">
                <View className="text-[#1f1f1f] font-bold mb-3 text-base flex items-center">
                  <View className="w-1 h-4 bg-[#FDD244] rounded-full mr-2"></View>
                  密码
                </View>
                <View className="bg-[#f8f9fa] rounded-xl p-3 border border-[#e9ecef] flex items-center">
                  <Input
                    className="bg-transparent text-base text-[#333] flex-1"
                    password
                    value={userAccount.password}
                    placeholder='请输入您的密码'
                    placeholderClass="text-[#999]"
                    cursorSpacing={20}
                    onInput={e => updateAccount({ password: e.detail.value })}
                  />
                  {userAccount.password && (
                    <View
                      className="w-6 h-6 bg-[#d1d5db] rounded-full flex items-center justify-center ml-2 active:bg-[#9ca3af]"
                      onClick={() => updateAccount({ password: '' })}
                    >
                      <View className="text-white text-sm font-bold leading-none">×</View>
                    </View>
                  )}
                </View>
              </View>
            </>
          ) : (
            // 短信验证码登录表单
            <>
              <View className="mb-4">
                <View className="text-[#1f1f1f] font-bold mb-3 text-base flex items-center">
                  <View className="w-1 h-4 bg-[#FDD244] rounded-full mr-2"></View>
                  手机号
                </View>
                <View className="bg-[#f8f9fa] rounded-xl p-3 border border-[#e9ecef] flex items-center">
                  <Input
                    className="bg-transparent text-base text-[#333] flex-1"
                    type='number'
                    value={smsForm.phone}
                    placeholder='请输入手机号'
                    placeholderClass="text-[#999]"
                    cursorSpacing={20}
                    onInput={e => updateSmsForm({ phone: e.detail.value })}
                  />
                  {smsForm.phone && (
                    <View
                      className="w-6 h-6 bg-[#d1d5db] rounded-full flex items-center justify-center ml-2 active:bg-[#9ca3af]"
                      onClick={() => updateSmsForm({ phone: '' })}
                    >
                      <View className="text-white text-sm font-bold leading-none">×</View>
                    </View>
                  )}
                </View>
              </View>
              <View className="mb-2">
                <View className="text-[#1f1f1f] font-bold mb-3 text-base flex items-center">
                  <View className="w-1 h-4 bg-[#FDD244] rounded-full mr-2"></View>
                  验证码
                </View>
                <View className="bg-[#f8f9fa] rounded-xl p-3 border border-[#e9ecef] flex items-center">
                  <Input
                    className="bg-transparent text-base text-[#333] flex-1"
                    type='number'
                    value={smsForm.verifyCode}
                    placeholder='请输入验证码'
                    placeholderClass="text-[#999]"
                    cursorSpacing={20}
                    onInput={e => updateSmsForm({ verifyCode: e.detail.value })}
                  />
                  {smsForm.verifyCode && (
                    <View
                      className="w-6 h-6 bg-[#d1d5db] rounded-full flex items-center justify-center ml-2 mr-2 active:bg-[#9ca3af]"
                      onClick={() => updateSmsForm({ verifyCode: '' })}
                    >
                      <View className="text-white text-sm font-bold leading-none">×</View>
                    </View>
                  )}
                  {showSend ? (
                    <Button
                      className="px-4 py-2 rounded-full bg-[#FDD244] text-white text-sm leading-none border-0 after:border-none shadow-sm active:bg-[#fdd244e0]"
                      hoverClass="bg-[#fdd244e0]"
                      onClick={sendVerifyCode}
                    >
                      获取验证码
                    </Button>
                  ) : (
                    <Label className="px-4 py-2 text-sm text-[#FDD244] leading-none bg-[rgba(253,210,68,0.1)] rounded-full">
                      {countdown}秒后重新发送
                    </Label>
                  )}
                </View>
              </View>
            </>
          )}
        </View>
      </View>

      {/* 额外操作区域 */}
      {activeTab === 'password' && (
        <View className="px-5 py-2.5 flex justify-end text-[#FDD244] text-sm">
          <View className="flex items-center" onClick={toVerifyCodeLogin}>
            <Image className="w-[18px] h-[18px] mr-1.5" src={require("@/assets/image/login/mima.png")} mode="aspectFit" />
            忘记密码
          </View>
        </View>
      )}

      {/* 短信登录提示 */}
      {activeTab === 'sms' && !smsForm.verifyCode && (
        <View className="px-5 py-2.5 text-center">
          <View className="text-sm text-[#999]">
            请先获取验证码后再登录
          </View>
        </View>
      )}

      {/* 登录按钮区域 */}
      <View className="flex flex-col gap-4 items-center text-[#999999] mt-6 px-5">
        <Button
          className={`w-full h-12 rounded-full border-0 text-lg font-bold shadow-lg after:border-none text-center flex items-center justify-center ${(activeTab === 'password' && userAccount.username && userAccount.password) ||
            (activeTab === 'sms' && smsForm.phone && smsForm.verifyCode)
            ? 'bg-[#FDD244] text-white active:bg-[#fdd244e0]'
            : 'bg-[#E0E0E0] text-white'
            }`}
          hoverClass={
            (activeTab === 'password' && userAccount.username && userAccount.password) ||
              (activeTab === 'sms' && smsForm.phone && smsForm.verifyCode)
              ? "bg-[#fdd244e0] shadow-xl"
              : ""
          }
          onClick={activeTab === 'password' ? login : loginWithSms}
        >
          登录
        </Button>
        <View className="text-[#999999] text-sm">OR</View>
        <Button
          className="w-full h-11 bg-[rgba(253,210,68,0.15)] text-[#FDD244] rounded-full border border-[#FDD244] text-base font-medium after:border-none active:bg-[rgba(253,210,68,0.25)] text-center flex items-center justify-center"
          hoverClass="bg-[rgba(253,210,68,0.25)]"
          openType="getPhoneNumber"
          onGetPhoneNumber={phoneLogin}
        >
          手机号一键登录
        </Button>
      </View>

      {/* 弹性空间 */}
      <View className="flex-1"></View>

      {/* 协议区域 - 添加合适的底部边距 */}
      <View className="text-sm text-[#999999] text-center flex items-center justify-center mb-8 px-4 active:bg-[rgba(0,0,0,0.05)]"
        onClick={() => setAgreeProtocol(state => !state)}>
        <View className="w-[20px] h-[20px] rounded-full border-2 border-[#FDD244] mr-2 flex justify-center items-center bg-white">
          {agreeProtocol && (
            <Image
              className="w-[14px] h-[14px]"
              src={require("@/assets/image/login/check-fill.svg")}
              mode="aspectFit"
            />
          )}
        </View>
        <Label className="text-sm leading-relaxed">
          阅读并同意
          <Label className="text-[#333333] font-bold" onClick={toViewProtocol}>用户协议</Label>
          和
          <Label className="text-[#333333] font-bold" onClick={toViewPolicy}>隐私政策</Label>
          并理解相关条款
        </Label>
      </View>
    </View>
  );
};

export default LoginPage;
